"""
WebSocket聊天响应VO类
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field

from robot_studio.common.base_result import BaseResult


class WebSocketChatDelta(BaseModel):
    """WebSocket聊天增量内容"""
    role: str = Field(..., description="角色")
    content: str = Field(..., description="内容")
    message_type: str = Field(..., description="消息类型")


class WebSocketChatData(BaseModel):
    """WebSocket聊天数据"""
    message_id: str = Field(..., description="消息ID")
    chunk_id: Optional[str] = Field(default=None, description="块ID")
    delta: List[WebSocketChatDelta] = Field(..., description="增量内容")
    artifacts: List[Dict[str, Any]] = Field(default_factory=list, description="产物列表")
    chunk_type: str = Field(..., description="块类型")
    chunk_sub_type: str = Field(..., description="块子类型")
    finish_reason: Optional[str] = Field(default=None, description="完成原因")
    usage_info: Optional[Dict[str, Any]] = Field(default=None, description="使用信息")
    created_at: str = Field(..., description="创建时间")
    error: Optional[str] = Field(default=None, description="错误信息")

class WebSocketChatResult(BaseResult):
    """WebSocket聊天结果"""
    code: Optional[int] = Field(default=200, description="HTTP状态码")
    message: Optional[str] = Field(default=None, description="响应消息")
    data: Optional[WebSocketChatData] = Field(default=None, description="聊天数据")

    @classmethod
    def success_chunk_result(
        cls,
        message_id: str,
        content: str,
        chunk_id: Optional[str] = None,
        chunk_type: str = "Event",
        chunk_sub_type: str = "流式",
        artifacts: Optional[List[Dict[str, Any]]] = None
    ) -> "WebSocketChatResult":
        """创建成功的流式块结果"""
        return cls(
            success=True,
            code=200,
            data=WebSocketChatData(
                message_id=message_id,
                chunk_id=chunk_id,
                delta=[WebSocketChatDelta(
                    role="assistant",
                    content=content,
                    message_type="text"
                )],
                artifacts=artifacts or [],
                chunk_type=chunk_type,
                chunk_sub_type=chunk_sub_type,
                created_at=datetime.now().isoformat()
            )
        )

    @classmethod
    def completion_result(
        cls,
        message_id: str,
        full_content: Optional[str] = None,
        artifacts: Optional[List[Dict[str, Any]]] = None,
        usage_info: Optional[Dict[str, Any]] = None
    ) -> "WebSocketChatResult":
        """创建完成结果"""
        return cls(
            success=True,
            code=200,
            data=WebSocketChatData(
                message_id=message_id,
                delta=[WebSocketChatDelta(
                    role="assistant",
                    content=full_content,
                    message_type="text"
                )],
                artifacts=artifacts or [],
                chunk_type="TaskFinish",
                chunk_sub_type="TaskFinish",
                finish_reason="stop",
                usage_info=usage_info,
                created_at=datetime.now().isoformat()
            )
        )

    @classmethod
    def error_result(cls, message_id: str, error_message: str) -> "WebSocketChatResult":
        """创建错误结果"""
        return cls(
            success=False,
            code=500,
            message="生成响应失败",
            data=WebSocketChatData(
                message_id=message_id,
                delta=[],
                artifacts=[],
                chunk_type="Error",
                chunk_sub_type="error",
                error=error_message,
                created_at=datetime.now().isoformat()
            )
        )


class PongResult(BaseModel):
    """心跳响应结果"""
    type: str = "pong"
    timestamp: float = Field(default_factory=lambda: datetime.now().timestamp() * 1000)


class AckResult(BaseModel):
    """确认响应结果"""
    type: str = "ack"
    id: str
    timestamp: float = Field(default_factory=lambda: datetime.now().timestamp() * 1000)


class ErrorResult(BaseModel):
    """错误响应结果"""
    type: str = "error"
    data: Dict[str, Any]
    
    @classmethod
    def create(cls, error_message: str) -> "ErrorResult":
        """创建错误结果"""
        return cls(
            data={
                "error": error_message,
                "timestamp": datetime.now().timestamp() * 1000
            }
        )
